#!/usr/bin/env python3
"""
Test script for the new multi-agent system.
This script verifies that all agents can be imported and initialized correctly.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_agents():
    """Test the new multi-agent system."""
    print("🚀 Testing New Multi-Agent System")
    print("=" * 50)
    
    try:
        # Test Planner Agent
        print("\n📋 Testing Planner Agent...")
        from agents.planner.agent import create_agent as create_planner
        planner_agent, planner_stack = await create_planner()
        print(f"✅ Planner Agent created: {planner_agent.name}")
        await planner_stack.__aexit__(None, None, None)
        
        # Test Executor Agent
        print("\n⚡ Testing Executor Agent...")
        from agents.executor.agent import create_agent as create_executor
        executor_agent, executor_stack = await create_executor()
        print(f"✅ Executor Agent created: {executor_agent.name}")
        await executor_stack.__aexit__(None, None, None)
        
        # Test Checker Agent
        print("\n🔍 Testing Checker Agent...")
        from agents.checker.agent import create_checker_agent
        checker_agent = create_checker_agent()
        print(f"✅ Checker Agent created: {checker_agent.name}")
        
        # Test Error Handler Agent
        print("\n🛠️ Testing Error Handler Agent...")
        from agents.error_handler.agent import create_agent as create_error_handler
        error_handler_agent, error_handler_stack = await create_error_handler()
        print(f"✅ Error Handler Agent created: {error_handler_agent.name}")
        await error_handler_stack.__aexit__(None, None, None)
        
        # Test Task Coordinator Agent
        print("\n🎯 Testing Task Coordinator Agent...")
        from agents.task_coordinator.agent import create_task_coordinator_agent
        coordinator_agent, coordinator_stack = await create_task_coordinator_agent()
        print(f"✅ Task Coordinator Agent created: {coordinator_agent.name}")
        print(f"   - Has Planner: {hasattr(coordinator_agent, 'planner_agent')}")
        print(f"   - Has Executor: {hasattr(coordinator_agent, 'executor_agent')}")
        print(f"   - Has Checker: {hasattr(coordinator_agent, 'checker_agent')}")
        print(f"   - Has Error Handler: {hasattr(coordinator_agent, 'error_handler_agent')}")
        await coordinator_stack.__aexit__(None, None, None)
        
        print("\n🎉 All agents created successfully!")
        print("\n📝 Agent Summary:")
        print("   1. Planner Agent - Processes user input and creates task plans")
        print("   2. Executor Agent - Executes tasks using terminal and desktop tools")
        print("   3. Checker Agent - Verifies task completion against indicators")
        print("   4. Error Handler Agent - Processes errors and provides solutions")
        print("   5. Task Coordinator Agent - Orchestrates the entire workflow")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing agents: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test function."""
    success = await test_agents()
    if success:
        print("\n✅ Multi-agent system test completed successfully!")
        return 0
    else:
        print("\n❌ Multi-agent system test failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
