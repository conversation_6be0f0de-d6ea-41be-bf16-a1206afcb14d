import os
from contextlib import AsyncExitStack
from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm
from dotenv import load_dotenv

# Sub-agent factories
from agents.planner.agent import create_agent as create_planner_agent
from agents.executor.agent import create_agent as create_executor_agent
from agents.checker.agent import create_checker_agent
from agents.error_handler.agent import create_agent as create_error_handler_agent

# Load environment variables (for OPENAI_API_KEY)
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '..', '.env'))

async def create_task_coordinator_agent():
    """Creates the Task Coordinator agent that orchestrates the multi-agent task execution system."""

    # Manage multiple exit stacks for async sub-agents
    exit_stack = AsyncExitStack()
    await exit_stack.__aenter__()

    # Instantiate Planner (async) and enter its exit stack
    planner_agent, planner_stack = await create_planner_agent()
    await exit_stack.enter_async_context(planner_stack)

    # Instantiate Executor (async) and enter its exit stack
    executor_agent, executor_stack = await create_executor_agent()
    await exit_stack.enter_async_context(executor_stack)

    # Instantiate Checker (sync)
    checker_agent = create_checker_agent()

    # Instantiate Error Handler (async) and enter its exit stack
    error_handler_agent, error_handler_stack = await create_error_handler_agent()
    await exit_stack.enter_async_context(error_handler_stack)

    # Define LLM using GPT-4o
    coordinator_llm = LiteLlm(model="gpt-4o", api_key=os.environ.get("OPENAI_API_KEY"))

    # Create the Task Coordinator agent
    coordinator = Agent(
        name="task_coordinator_agent",
        description="Coordinates a multi-agent system for intelligent task planning, execution, checking, and error handling.",
        model=coordinator_llm,
        instruction=(
            "You are the Task Coordinator agent managing a sophisticated multi-agent system. Your workflow is:\n\n"
            "**PHASE 1: PLANNING**\n"
            "1. When a user provides a task request, delegate it to the Planner agent\n"
            "2. The Planner will process the request through web interface and return:\n"
            "   - TASK_PLANNING: Detailed execution steps\n"
            "   - CHECK_INDICATORS: Completion verification criteria\n\n"
            "**PHASE 2: EXECUTION**\n"
            "3. Send the TASK_PLANNING to the Executor agent\n"
            "4. The Executor will perform the tasks using terminal, desktop, and file operations\n\n"
            "**PHASE 3: VERIFICATION**\n"
            "5. Send the CHECK_INDICATORS to the Checker agent\n"
            "6. The Checker will request information from the Executor and verify completion\n"
            "7. If all indicators are met, report SUCCESS\n\n"
            "**PHASE 4: ERROR HANDLING (if needed)**\n"
            "8. If the Checker finds incomplete tasks, send the feedback to the Error Handler\n"
            "9. The Error Handler will process errors through web interface and provide solutions\n"
            "10. Send the solutions back to the Executor for implementation\n"
            "11. Return to PHASE 3 for re-verification\n\n"
            "**COORDINATION RULES:**\n"
            "- Always follow the phases in order\n"
            "- Clearly communicate between agents using their specific roles\n"
            "- Maintain context throughout the entire workflow\n"
            "- Provide status updates to the user at each major phase\n"
            "- Continue the error handling loop until all tasks are completed\n"
            "- Generate a final comprehensive report when all tasks are successfully completed\n\n"
            "Manage the entire workflow efficiently and ensure all agents work together seamlessly."
        ),
        tools=[],  # Coordinator doesn't need direct tools, it delegates to sub-agents
    )

    # Store sub-agents as attributes for delegation
    coordinator.planner_agent = planner_agent
    coordinator.executor_agent = executor_agent
    coordinator.checker_agent = checker_agent
    coordinator.error_handler_agent = error_handler_agent

    return coordinator, exit_stack

# Create the root agent for ADK - following the existing pattern
root_agent = create_task_coordinator_agent()
