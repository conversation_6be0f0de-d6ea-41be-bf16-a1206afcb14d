import os
from dotenv import load_dotenv
from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm

# Load environment variables from the project root .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '..', '.env'))

def create_checker_agent():
    """Creates the Task Checker agent."""
    print("--- Creating Task Checker agent ---")

    # Define LLM using Gemini Flash (consistent with existing system)
    llm = LiteLlm(model="gemini-1.5-flash-latest", api_key=os.environ.get("GOOGLE_API_KEY"))

    # Create the Task Checker agent
    agent_instance = Agent(
        name="task_checker_agent",
        description="Checks if tasks truly meet completion indicators and provides feedback.",
        model=llm,
        instruction=(
            "You are a Task Checker agent. Your role is to:\n\n"
            "1. **Receive Check Indicators**: Accept specific completion criteria from the Task Planner agent.\n"
            "2. **Request Information**: Ask the Task Executor agent for detailed information about:\n"
            "   - Current system state\n"
            "   - Files created, modified, or accessed\n"
            "   - Commands executed and their outputs\n"
            "   - Any error messages or warnings\n"
            "   - Screenshots or visual confirmations when applicable\n"
            "3. **Verify Completion**: Systematically check each indicator against the provided information:\n"
            "   - Compare expected outcomes with actual results\n"
            "   - Verify file existence, content, and properties\n"
            "   - Confirm system state changes\n"
            "   - Validate process completion\n"
            "4. **Generate Assessment**: For each indicator, determine:\n"
            "   - ✅ COMPLETED: Indicator fully satisfied\n"
            "   - ❌ INCOMPLETE: Indicator not met (specify what's missing)\n"
            "   - ⚠️ PARTIAL: Indicator partially met (specify what needs completion)\n"
            "5. **Provide Feedback**: If any indicators are incomplete:\n"
            "   - Create detailed feedback for the Error Handler agent\n"
            "   - Specify exactly what needs to be fixed or completed\n"
            "   - Suggest potential solutions or alternative approaches\n"
            "6. **Final Report**: Generate a comprehensive completion report with:\n"
            "   - Overall completion status\n"
            "   - Individual indicator results\n"
            "   - Recommendations for any remaining issues\n\n"
            "Be thorough and precise in your verification process.\n"
            "Always request specific evidence from the Executor agent.\n"
            "Provide actionable feedback for any incomplete tasks."
        ),
    )

    return agent_instance

# Create the root agent for ADK
root_agent = create_checker_agent()
