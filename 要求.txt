有##符号的表示暂时不用实现
1.基于ADK进行多智能体系统搭建，工具调用使用MCP
2.设置agent
 （1）模型：gemini-1.5-flash-latest 功能：将用户输入传入网页端进行处理,要求分为详细任务规划部分和检查指标部分 (MCP:ai_knowledge_bridge)，将网页端输出任务规划部分传入执行者agent2,检查指标部分传入agnet3
 （2）模型：gemini-1.5-flash-latest 功能：将任务规划依次执行，控制终端,桌面操作,文件系统操作,操作时尽可能使用终端指令和快捷键实现  （MCP：AutoOmni,desktop-commander）
 （3）模型：gemini-1.5-flash-latest 功能：检查任务是否真正达到完成指标，将检查指标传入agent2要求agent2提供对应信息,agent3根据信息判断指标是否完成,若没有全部完成向agent4反馈
 （4）模型：gemini-1.5-flash-latest 功能：将报错或不足传入网页端进行处理 (MCP:ai_knowledge_bridge),将网页端输出传入agent2让它执行
  ##(5) 模型：gemini-1.5-flash-latest 功能：借由qq或微信进行效果展示和指令收发 （QQ_bot_mcp）
##3.使用一个任务清单使得系统可以依次自动执行要求，不断更新




mcp引入方法：
{
  "mcpServers": {

    "desktop-commander": {
      "command": "npx",
      "args": [
        "-y",
        "@wonderwhy-er/desktop-commander"
      ]
    },
    "blender": {
        "command": "uvx",
        "args": [
            "blender-mcp"
        ]
    },
    "ai_knowledge_bridge": {
      "runtime": "python",
      "command": "python",
      "args": [
        "c:/dev/MCP/ai__information_share/run_ai_knowledge_bridge.py"
      ]
    },
    "auto-omni": {
      "runtime": "python",
      "command": "python",
      "args": [
        "c:/dev/MCP/AutoOmni/auto_omni_mcp.py"
      ],
      "port": 5001
    }



  }
}

