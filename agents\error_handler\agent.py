import asyncio
import os
from dotenv import load_dotenv
from google.adk.agents import Agent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from google.adk.models.lite_llm import LiteLlm

# Load environment variables from the project root .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '..', '.env'))

async def create_agent():
    """Creates the Error Handler agent by connecting to the ai_knowledge_bridge MCP server."""
    print("--- Attempting to start and connect to ai_knowledge_bridge MCP server for Error Handler ---")

    tools, exit_stack = await MCPToolset.from_server(
        connection_params=StdioServerParameters(
            command='python',
            args=['c:/dev/MCP/ai__information_share/run_ai_knowledge_bridge.py'],
            env=os.environ.copy()
        )
    )

    print(f"--- Connected to ai_knowledge_bridge for Error Handler. Discovered {len(tools)} tool(s). ---")
    for tool in tools:
        print(f"  - Discovered tool: {tool.name}")

    # Define LLM using GPT-4o
    llm = LiteLlm(model="gpt-4o", api_key=os.environ.get("OPENAI_API_KEY"))

    # Create the Error Handler agent
    agent_instance = Agent(
        name="error_handler_agent",
        description="Processes errors and deficiencies through web interface and provides solutions.",
        model=llm,
        instruction=(
            "You are an Error Handler agent. Your role is to:\n\n"
            "1. **Receive Error Reports**: Accept detailed feedback from the Task Checker agent about:\n"
            "   - Incomplete tasks\n"
            "   - Failed operations\n"
            "   - Missing requirements\n"
            "   - System errors or issues\n"
            "2. **Process Through Web Interface**: Use ai_knowledge_bridge MCP tools to:\n"
            "   - Send error details to the web interface for analysis\n"
            "   - Get expert recommendations and solutions\n"
            "   - Obtain alternative approaches or troubleshooting steps\n"
            "3. **Analyze Web Response**: Parse the web interface output to extract:\n"
            "   - Root cause analysis\n"
            "   - Recommended solutions\n"
            "   - Alternative approaches\n"
            "   - Preventive measures\n"
            "4. **Generate Corrective Actions**: Create detailed instructions for the Task Executor including:\n"
            "   - Specific steps to fix identified issues\n"
            "   - Alternative commands or approaches\n"
            "   - Verification steps to ensure fixes work\n"
            "   - Rollback procedures if needed\n"
            "5. **Format Output**: Provide clear, actionable instructions in the format:\n"
            "   - PROBLEM_ANALYSIS: [detailed issue description]\n"
            "   - SOLUTION_STEPS: [step-by-step fix instructions]\n"
            "   - VERIFICATION: [how to confirm the fix worked]\n"
            "6. **Iterative Support**: Continue providing solutions until all issues are resolved.\n\n"
            "Always use the ai_knowledge_bridge tools to leverage web-based expertise.\n"
            "Provide comprehensive, actionable solutions.\n"
            "Focus on both immediate fixes and long-term prevention."
        ),
        tools=tools,
    )

    return agent_instance, exit_stack

# Create the root agent for ADK - following the existing pattern
root_agent = create_agent()
