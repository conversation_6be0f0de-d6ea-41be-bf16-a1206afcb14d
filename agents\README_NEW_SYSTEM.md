# New Multi-Agent Task Execution System

This document describes the new multi-agent system created based on the requirements in `要求.txt`. The system consists of 4 specialized agents coordinated by a main Task Coordinator agent.

## System Architecture

```
User Input
    ↓
Task Coordinator Agent
    ↓
┌─────────────────────────────────────────────────────────────┐
│                    Multi-Agent Workflow                     │
├─────────────────────────────────────────────────────────────┤
│ 1. Planner Agent (ai_knowledge_bridge)                     │
│    ├─ Processes user input through web interface           │
│    ├─ Splits output into task planning and check indicators│
│    └─ Returns structured planning data                     │
│                                                             │
│ 2. Executor Agent (AutoOmni + desktop-commander)           │
│    ├─ Executes task planning sequentially                  │
│    ├─ Controls terminal, desktop, and file operations      │
│    └─ Provides execution status and information            │
│                                                             │
│ 3. Checker Agent                                           │
│    ├─ Verifies task completion against indicators          │
│    ├─ Requests information from Executor                   │
│    └─ Reports completion status or issues                  │
│                                                             │
│ 4. Error Handler Agent (ai_knowledge_bridge)               │
│    ├─ Processes errors through web interface               │
│    ├─ Provides solutions and corrective actions            │
│    └─ Sends fixes back to Executor                         │
└─────────────────────────────────────────────────────────────┘
```

## Agent Descriptions

### 1. Planner Agent (`agents/planner/`)
- **Model**: GPT-4o
- **MCP Tools**: ai_knowledge_bridge
- **Function**: 
  - Processes user input through web interface
  - Splits response into detailed task planning and check indicators
  - Provides structured output for execution and verification

### 2. Executor Agent (`agents/executor/`)
- **Model**: GPT-4o
- **MCP Tools**: AutoOmni, desktop-commander
- **Function**:
  - Executes task planning step by step
  - Controls terminal operations, desktop actions, and file system
  - Prioritizes terminal commands and keyboard shortcuts
  - Provides detailed execution information when requested

### 3. Checker Agent (`agents/checker/`)
- **Model**: GPT-4o
- **MCP Tools**: None (uses direct communication)
- **Function**:
  - Verifies task completion against specific indicators
  - Requests detailed information from Executor agent
  - Generates comprehensive completion reports
  - Provides feedback for incomplete tasks

### 4. Error Handler Agent (`agents/error_handler/`)
- **Model**: GPT-4o
- **MCP Tools**: ai_knowledge_bridge
- **Function**:
  - Processes errors and deficiencies through web interface
  - Analyzes root causes and provides solutions
  - Generates corrective action plans
  - Supports iterative problem resolution

### 5. Task Coordinator Agent (`agents/task_coordinator/`)
- **Model**: GPT-4o
- **MCP Tools**: None (orchestrates other agents)
- **Function**:
  - Manages the complete workflow across all agents
  - Coordinates communication between agents
  - Handles the iterative error correction loop
  - Provides status updates and final reports

## Workflow Process

### Phase 1: Planning
1. User submits task request to Task Coordinator
2. Coordinator delegates to Planner Agent
3. Planner processes request through web interface (ai_knowledge_bridge)
4. Planner returns structured output:
   - `TASK_PLANNING`: Detailed execution steps
   - `CHECK_INDICATORS`: Completion verification criteria

### Phase 2: Execution
1. Coordinator sends task planning to Executor Agent
2. Executor performs tasks using available MCP tools:
   - Terminal operations (AutoOmni)
   - Desktop operations (desktop-commander)
   - File system operations
3. Executor reports progress and maintains execution state

### Phase 3: Verification
1. Coordinator sends check indicators to Checker Agent
2. Checker requests detailed information from Executor
3. Checker verifies each completion indicator
4. Checker reports overall completion status

### Phase 4: Error Handling (if needed)
1. If verification fails, Coordinator sends issues to Error Handler
2. Error Handler processes problems through web interface
3. Error Handler provides detailed corrective actions
4. Coordinator sends solutions to Executor for implementation
5. Process returns to Phase 3 for re-verification

## MCP Server Configuration

The system requires the following MCP servers to be configured:

```json
{
  "mcpServers": {
    "desktop-commander": {
      "command": "npx",
      "args": ["-y", "@wonderwhy-er/desktop-commander"]
    },
    "ai_knowledge_bridge": {
      "runtime": "python",
      "command": "python",
      "args": ["c:/dev/MCP/ai__information_share/run_ai_knowledge_bridge.py"]
    },
    "auto-omni": {
      "runtime": "python",
      "command": "python",
      "args": ["c:/dev/MCP/AutoOmni/auto_omni_mcp.py"],
      "port": 5001
    }
  }
}
```

## Environment Variables

Ensure the following environment variables are set in your `.env` file:

```env
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
```

## Usage

### Running Individual Agents
Each agent can be run independently using the ADK framework:

```bash
# Navigate to specific agent directory
cd agents/planner
adk run

# Or use the web interface
adk web
```

### Running the Complete System
Use the Task Coordinator agent for the full multi-agent workflow:

```bash
cd agents/task_coordinator
adk run
```

### Testing the System
Run the test script to verify all agents are working:

```bash
python test_new_agents.py
```

## Key Features

1. **Intelligent Planning**: Web-based task analysis and planning
2. **Robust Execution**: Multi-tool task execution with terminal and desktop control
3. **Comprehensive Verification**: Detailed completion checking against specific indicators
4. **Automatic Error Recovery**: Web-based error analysis and solution generation
5. **Iterative Improvement**: Continuous error correction until task completion
6. **Modular Design**: Each agent can be used independently or as part of the system

## Future Enhancements

- Integration with QQ/WeChat bots for remote control (Agent 5 - currently marked as optional)
- Task queue management for automated sequential execution
- Enhanced logging and monitoring capabilities
- Performance optimization and caching mechanisms
